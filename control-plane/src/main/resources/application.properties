spring.application.name=control-plane-service
server.port=9080
server.servlet.context-path=/

control.plane.topic.prefix=control-plane-
kafka.bootstrap_brokers=localhost:9092

########################
## Swagger properties ##
########################
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.csrf.enabled=true
springdoc.show-actuator=true
management.endpoints.web.exposure.include=health,prometheus
management.metrics.tags.service=${spring.application.name}
management.security.enabled=true

###########################
## Datasource properties ##
###########################
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url= *****************************************
spring.datasource.username=postgres
spring.datasource.password=Ujjwal@6886

####################
## JPA properties ##
####################
spring.jpa.database=POSTGRESQL
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.query.plan_cache_max_size=256
spring.jpa.properties.hibernate.id.new_generator_mappings=true
#spring.jpa.hibernate.ddl-auto=create
spring.jpa.generate-ddl=true

############################
## App Auth Server config ##
############################
controller.baseurl=http://localhost:9080
oauth2.auth-server.baseurl=https://${urls.authentication_url}
oauth2.auth-server.realm=databahn
urls.authentication_url=id.galaxy.dabahn.app

################################
## Resource server essentials ##
################################
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/certs

######################
## OAuth2 for Feign ##
######################
spring.cloud.openfeign.oauth2.enabled=true
spring.cloud.openfeign.oauth2.clientRegistrationId=dataplane-client
spring.security.oauth2.client.registration.dataplane-client.client-id=${oauth.backend_client_id}
spring.security.oauth2.client.registration.dataplane-client.client-secret=${oauth.backend_client_secret}
spring.security.oauth2.client.registration.dataplane-client.scope=openid
spring.security.oauth2.client.registration.dataplane-client.authorization-grant-type=client_credentials

spring.security.oauth2.client.provider.dataplane-client.authorization-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/auth
spring.security.oauth2.client.provider.dataplane-client.token-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/token
spring.security.oauth2.client.provider.dataplane-client.jwk-set-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/certs
spring.security.oauth2.client.provider.dataplane-client.user-info-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/userinfo
spring.security.oauth2.client.provider.dataplane-client.user-name-attribute=preferred_username

################################
## Logging Config ##
################################
logging.level.com.databahn.planes.controlplane = ${LOG_LEVEL:INFO}
logging.level.root = INFO

open_search.username=admin
open_search.password=Ujjwal@6886
open_search.url=https://localhost:9200
open_search.stats_index=db_statistics
spring.cloud.vault.enabled=false
package com.databahn.planes.dataplane.client;

import com.databahn.planes.dataplane.config.ControlPlaneClientConfig;
import com.databahn.planes.response.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Dedicated Feign client for metrics endpoints that require special octet-stream encoding. This
 * client is configured with MetricsClientConfig to handle raw string data properly.
 */
@FeignClient(
    value = "metrics-controlplane-client",
    url = "${controller.baseurl}",
    configuration = ControlPlaneClientConfig.class)
public interface MetricsControlPlaneClient {

  /**
   * Sends metrics data to the control plane using octet-stream encoding.
   *
   * @param metricType The type of metric (e.g., "kafka", "redis", "kubernetes", "node")
   * @param metricsData The raw metrics data as a string
   * @return Response from the control plane
   */
  @PostMapping(
      value = "/v1/metrics/{type}",
      consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  Response<Object> saveMetrics(
      @PathVariable("type") String metricType, @RequestBody byte[] metricsData);
}

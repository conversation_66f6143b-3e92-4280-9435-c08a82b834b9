spring.application.name=data-plane-service
server.port=8081
server.servlet.context-path=/
spring.config.import=optional:file:/opt/databahn/config/app21.yaml

# Kafka configuration for local development
kafka.input.bootstrap_brokers=localhost:9092
kafka.processing.bootstrap_brokers=localhost:9092

########################
## Swagger properties ##
########################
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.csrf.enabled=true
springdoc.show-actuator=true
management.endpoints.web.exposure.include=health,prometheus
management.metrics.tags.service=${spring.application.name}
management.security.enabled=true
management.health.redis.enabled=false

############################
## App Auth Server config ##
############################
controller.baseurl=http://localhost:9080
dataplane.baseurl=http://localhost:8083
oauth2.auth-server.baseurl=https://${urls.authentication_url}
oauth2.auth-server.realm=databahn

################################
## Resource server essentials ##
################################
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/certs

######################
## OAuth2 for Feign ##
######################
spring.cloud.openfeign.oauth2.clientRegistrationId=controlplane-client
spring.security.oauth2.client.registration.controlplane-client.client-id=${oauth.backend_client_id}
spring.security.oauth2.client.registration.controlplane-client.client-secret=${oauth.backend_client_secret}
spring.security.oauth2.client.registration.controlplane-client.scope=openid
spring.security.oauth2.client.registration.controlplane-client.authorization-grant-type=client_credentials

spring.security.oauth2.client.provider.controlplane-client.authorization-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/auth
spring.security.oauth2.client.provider.controlplane-client.token-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/token
spring.security.oauth2.client.provider.controlplane-client.jwk-set-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/certs
spring.security.oauth2.client.provider.controlplane-client.user-info-uri=${oauth2.auth-server.baseurl}/realms/${oauth2.auth-server.realm}/protocol/openid-connect/userinfo
spring.security.oauth2.client.provider.controlplane-client.user-name-attribute=preferred_username


################################
## Logging Config ##
################################
logging.level.com.databahn.planes.dataplane = ${LOG_LEVEL:ERROR}
logging.level.root = INFO